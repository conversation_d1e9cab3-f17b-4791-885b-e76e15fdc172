package api

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/anthdm/ggcommerce/types"
	"github.com/stretchr/testify/assert"
)

type mockUserStore struct {
	InsertFn     func(ctx context.Context, user *types.User) error
	GetAllFn     func(ctx context.Context) ([]*types.User, error)
	GetByIDFn    func(ctx context.Context, id string) (*types.User, error)
	GetByEmailFn func(ctx context.Context, email string) (*types.User, error)
}

func (m *mockUserStore) Insert(ctx context.Context, user *types.User) error {
	return m.InsertFn(ctx, user)
}
func (m *mockUserStore) GetAll(ctx context.Context) ([]*types.User, error) {
	return m.GetAllFn(ctx)
}
func (m *mockUserStore) GetByID(ctx context.Context, id string) (*types.User, error) {
	return m.GetByIDFn(ctx, id)
}
func (m *mockUserStore) GetByEmail(ctx context.Context, email string) (*types.User, error) {
	return m.GetByEmailFn(ctx, email)
}

// Helper test context that mocks the minimal weavebox.Context API needed for handlers
type testContext struct {
	req    *http.Request
	rw     http.ResponseWriter
	params map[string]string
}

func (c *testContext) Request() *http.Request              { return c.req }
func (c *testContext) ResponseWriter() http.ResponseWriter { return c.rw }
func (c *testContext) Param(key string) string {
	if c.params == nil {
		return ""
	}
	return c.params[key]
}

// Add Context field for passing context.Context if needed
func (c *testContext) Context() context.Context { return c.req.Context() }

// Add JSON method for handler responses
type testHTTPError struct {
	Code int
	Body interface{}
}

func (e *testHTTPError) Error() string {
	return http.StatusText(e.Code)
}

func (c *testContext) JSON(code int, v interface{}) error {
	c.rw.WriteHeader(code)
	_ = json.NewEncoder(c.rw).Encode(v)
	if code >= 400 {
		return &testHTTPError{Code: code, Body: v}
	}
	return nil
}

// Add Header method for middleware
func (c *testContext) Header(key string) string {
	return c.req.Header.Get(key)
}

func newTestContext(method, path string, body []byte, params map[string]string) (*testContext, *httptest.ResponseRecorder) {
	req := httptest.NewRequest(method, path, bytes.NewReader(body))
	rw := httptest.NewRecorder()
	ctx := &testContext{req: req, rw: rw, params: params}
	return ctx, rw
}

func TestHandlePostUser_Success(t *testing.T) {
	store := &mockUserStore{
		InsertFn: func(ctx context.Context, user *types.User) error { return nil },
	}
	h := NewUserHandler(store)
	userReq := types.CreateUserRequest{Email: "<EMAIL>", Password: "pass123", IsAdmin: false}
	body, _ := json.Marshal(userReq)
	ctx, rw := newTestContext("POST", "/users", body, nil)

	err := h.HandlePostUser(ctx)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, rw.Code)
}

func TestHandlePostUser_InvalidBody(t *testing.T) {
	store := &mockUserStore{}
	h := NewUserHandler(store)
	ctx, rw := newTestContext("POST", "/users", []byte("notjson"), nil)
	err := h.HandlePostUser(ctx)
	assert.Error(t, err)
	assert.Equal(t, http.StatusBadRequest, rw.Code)
}

func TestHandleGetUsers_Success(t *testing.T) {
	store := &mockUserStore{
		GetAllFn: func(ctx context.Context) ([]*types.User, error) {
			return []*types.User{{ID: "1", Email: "<EMAIL>"}}, nil
		},
	}
	h := NewUserHandler(store)
	ctx, rw := newTestContext("GET", "/users", nil, nil)
	err := h.HandleGetUsers(ctx)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rw.Code)
}

func TestHandleGetUserByID_Found(t *testing.T) {
	store := &mockUserStore{
		GetByIDFn: func(ctx context.Context, id string) (*types.User, error) {
			return &types.User{ID: id, Email: "<EMAIL>"}, nil
		},
	}
	h := NewUserHandler(store)
	ctx, rw := newTestContext("GET", "/users/1", nil, map[string]string{"id": "1"})
	err := h.HandleGetUserByID(ctx)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rw.Code)
}

func TestHandleGetUserByID_NotFound(t *testing.T) {
	store := &mockUserStore{
		GetByIDFn: func(ctx context.Context, id string) (*types.User, error) {
			return nil, errors.New("mongo: no documents in result")
		},
	}
	h := NewUserHandler(store)
	ctx, rw := newTestContext("GET", "/users/2", nil, map[string]string{"id": "2"})
	err := h.HandleGetUserByID(ctx)
	assert.Error(t, err)
	assert.Equal(t, http.StatusNotFound, rw.Code)
}

func TestHandleLogin_Success(t *testing.T) {
	user, _ := types.NewUser("<EMAIL>", "pass123")
	user.ID = "1"
	user.IsAdmin = false
	store := &mockUserStore{
		GetByEmailFn: func(ctx context.Context, email string) (*types.User, error) {
			return user, nil
		},
	}
	h := NewUserHandler(store)
	loginReq := map[string]string{"email": "<EMAIL>", "password": "pass123"}
	body, _ := json.Marshal(loginReq)
	ctx, rw := newTestContext("POST", "/login", body, nil)

	err := h.HandleLogin(ctx)
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, rw.Code)
}

func TestHandleLogin_InvalidCredentials(t *testing.T) {
	store := &mockUserStore{
		GetByEmailFn: func(ctx context.Context, email string) (*types.User, error) {
			return nil, errors.New("not found")
		},
	}
	h := NewUserHandler(store)
	loginReq := map[string]string{"email": "<EMAIL>", "password": "pass123"}
	body, _ := json.Marshal(loginReq)
	ctx, rw := newTestContext("POST", "/login", body, nil)
	err := h.HandleLogin(ctx)
	assert.Error(t, err)
	assert.Equal(t, http.StatusUnauthorized, rw.Code)
}
