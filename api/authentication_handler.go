package api

import (
	"encoding/json"
	"net/http"

	"github.com/anthdm/ggcommerce/store"
	"github.com/anthdm/weavebox"
)

type AuthenticationRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type Authentication<PERSON><PERSON><PERSON> struct {
	userStore store.UserStorer
}

func NewAuthenticationHandler(userStore store.UserStorer) *AuthenticationHandler {
	return &AuthenticationHandler{
		userStore: userStore,
	}
}

func (h *AuthenticationHandler) AuthenticateUser(ctx *weavebox.Context) error {
	authReq := &AuthenticationRequest{}
	if err := json.NewDecoder(ctx.Request().Body).Decode(authReq); err != nil {
		return ctx.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	user, err := h.userStore.GetByEmail(ctx.Context, authReq.Email)
	if err != nil {
		return ctx.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid credentials"})
	}

	if !user.ValidatePassword(authReq.Password) {
		return ctx.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid credentials"})
	}

	return ctx.JSON(http.StatusOK, user)
}
