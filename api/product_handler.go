package api

import (
	"encoding/json"
	"net/http"

	"github.com/anthdm/ggcommerce/store"
	"github.com/anthdm/ggcommerce/types"

	"github.com/anthdm/weavebox"
)

type ProductHandler struct {
	store store.ProductStorer
}

func NewProductHandler(pStore store.ProductStorer) *ProductHandler {
	return &ProductHandler{
		store: pStore,
	}
}

// HandlePostProduct creates a new product from the request body with improved error handling and validation.
func (h *ProductHandler) HandlePostProduct(c *weavebox.Context) error {
	productReq := &types.CreateProductRequest{}
	if err := json.NewDecoder(c.Request().Body).Decode(productReq); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate input via NewProductFromRequest which calls validateCreateProductRequest
	product, err := types.NewProductFromRequest(productReq)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": err.Error()})
	}

	if err := h.store.Insert(c.Context, product); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to insert product"})
	}

	return c.JSON(http.StatusCreated, product)
}

// HandleGetProducts returns all products with improved error handling.
func (h *ProductHandler) HandleGetProducts(c *weavebox.Context) error {
	products, err := h.store.GetAll(c.Context)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch products"})
	}
	return c.JSON(http.StatusOK, products)
}

// HandleGetProductByID returns a product by its ID, with proper not found and error handling.
func (h *ProductHandler) HandleGetProductByID(c *weavebox.Context) error {
	id := c.Param("id")
	product, err := h.store.GetByID(c.Context, id)
	if err != nil {
		// Check for mongo.ErrNoDocuments (not found)
		if err.Error() == "mongo: no documents in result" {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "Product not found"})
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch product"})
	}
	return c.JSON(http.StatusOK, product)
}
