package api

import (
	"encoding/json"
	"errors"
	"net/http"

	"github.com/anthdm/ggcommerce/store"
	"github.com/anthdm/ggcommerce/types"

	"github.com/anthdm/weavebox"
)

type ProductHandler struct {
	store store.ProductStorer
}

func NewProductHandler(pStore store.ProductStorer) *ProductHandler {
	return &ProductHandler{
		store: pStore,
	}
}

// Common error responses
var (
	ErrInvalidRequestBody = errors.New("invalid request body")
	ErrProductNotFound    = errors.New("product not found")
	ErrInsertFailed       = errors.New("failed to insert product")
	ErrFetchFailed        = errors.New("failed to fetch product")
)

// Helper method to send error responses
func (h *ProductHandler) sendError(c *weavebox.Context, statusCode int, err error) error {
	return c.JSON(statusCode, map[string]string{"error": err.Error()})
}

// Helper method to decode request body
func (h *ProductHandler) decodeProductRequest(c *weavebox.Context) (*types.CreateProductRequest, error) {
	productReq := &types.CreateProductRequest{}
	if err := json.NewDecoder(c.Request().Body).Decode(productReq); err != nil {
		return nil, ErrInvalidRequestBody
	}
	return productReq, nil
}

// Helper method to check if error is "not found"
func (h *ProductHandler) isNotFoundError(err error) bool {
	return err.Error() == "mongo: no documents in result"
}

// HandlePostProduct creates a new product from the request body with improved error handling and validation.
func (h *ProductHandler) HandlePostProduct(c *weavebox.Context) error {
	productReq, err := h.decodeProductRequest(c)
	if err != nil {
		return h.sendError(c, http.StatusBadRequest, err)
	}

	product, err := types.NewProductFromRequest(productReq)
	if err != nil {
		return h.sendError(c, http.StatusBadRequest, err)
	}

	if err := h.store.Insert(c.Context, product); err != nil {
		return h.sendError(c, http.StatusInternalServerError, ErrInsertFailed)
	}

	return c.JSON(http.StatusCreated, product)
}

// HandleGetProducts returns all products with improved error handling.
func (h *ProductHandler) HandleGetProducts(c *weavebox.Context) error {
	products, err := h.store.GetAll(c.Context)
	if err != nil {
		return h.sendError(c, http.StatusInternalServerError, ErrFetchFailed)
	}
	return c.JSON(http.StatusOK, products)
}

// HandleGetProductByID returns a product by its ID, with proper not found and error handling.
func (h *ProductHandler) HandleGetProductByID(c *weavebox.Context) error {
	id := c.Param("id")
	product, err := h.store.GetByID(c.Context, id)
	if err != nil {
		if h.isNotFoundError(err) {
			return h.sendError(c, http.StatusNotFound, ErrProductNotFound)
		}
		return h.sendError(c, http.StatusInternalServerError, ErrFetchFailed)
	}
	return c.JSON(http.StatusOK, product)
}
