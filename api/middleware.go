package api

import (
	"errors"
	"os"

	"github.com/golang-jwt/jwt/v4"
)

// Authentication errors
var (
	ErrUnAuthenticated    = errors.New("unauthenticated")
	ErrInvalidToken       = errors.New("invalid token")
	ErrMissingToken       = errors.New("missing authentication token")
	ErrInvalidSigningMethod = errors.New("invalid signing method")
	ErrInvalidClaims      = errors.New("invalid token claims")
	ErrNotAdmin           = errors.New("admin privileges required")
)

// HeaderProvider defines a minimal interface for context header access
// This allows for easier testing and decouples from weavebox.Context
type HeaderProvider interface {
	Header(string) string
}

// JWTConfig holds JWT configuration
type JWTConfig struct {
	Secret    string
	TokenHeader string
}

// NewJWTConfig creates a new JWT configuration with defaults
func NewJWTConfig() *JWTConfig {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		panic("JWT_SECRET environment variable is required")
	}
	
	return &JWTConfig{
		Secret:      secret,
		TokenHeader: "x-api-token",
	}
}

// AdminAuthMiddleware handles admin authentication
type AdminAuthMiddleware struct {
	config *JWTConfig
}

// NewAdminAuthMiddleware creates a new admin authentication middleware
func NewAdminAuthMiddleware(config *JWTConfig) *AdminAuthMiddleware {
	return &AdminAuthMiddleware{
		config: config,
	}
}

// Authenticate validates admin authentication
func (mw *AdminAuthMiddleware) Authenticate(ctx HeaderProvider) error {
	tokenString := mw.extractToken(ctx)
	if tokenString == "" {
		return ErrMissingToken
	}

	token, err := mw.parseJWT(tokenString)
	if err != nil {
		return err
	}

	if !token.Valid {
		return ErrInvalidToken
	}

	claims, err := mw.extractClaims(token)
	if err != nil {
		return err
	}

	if !mw.isAdmin(claims) {
		return ErrNotAdmin
	}

	return nil
}

// extractToken gets the token from the request header
func (mw *AdminAuthMiddleware) extractToken(ctx HeaderProvider) string {
	return ctx.Header(mw.config.TokenHeader)
}

// extractClaims safely extracts claims from the token
func (mw *AdminAuthMiddleware) extractClaims(token *jwt.Token) (jwt.MapClaims, error) {
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, ErrInvalidClaims
	}
	return claims, nil
}

// isAdmin checks if the user has admin privileges
func (mw *AdminAuthMiddleware) isAdmin(claims jwt.MapClaims) bool {
	isAdminVal, exists := claims["isAdmin"]
	if !exists {
		return false
	}

	switch v := isAdminVal.(type) {
	case bool:
		return v
	case float64:
		return v == 1
	case string:
		return v == "true" || v == "1"
	default:
		return false
	}
}

// parseJWT parses and validates a JWT token
func (mw *AdminAuthMiddleware) parseJWT(tokenString string) (*jwt.Token, error) {
	return jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, ErrInvalidSigningMethod
		}
		return []byte(mw.config.Secret), nil
	})
}
