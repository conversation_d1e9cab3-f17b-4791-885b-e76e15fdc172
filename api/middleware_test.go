package api

import (
	"os"
	"testing"

	"github.com/golang-jwt/jwt/v4"
	"github.com/stretchr/testify/assert"
)

type mockContext struct {
	headers map[string]string
}

func (c *mockContext) Header(key string) string {
	return c.headers[key]
}

func generateTestJWT(isAdmin bool, secret string) (string, error) {
	claims := jwt.MapClaims{
		"isAdmin": isAdmin,
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}
	return tokenString, nil
}

func TestAdminAuthMiddleware_Authenticate(t *testing.T) {
	os.Setenv("JWT_SECRET", "testsecret")
	defer os.Unsetenv("JWT_SECRET")
	
	config := NewJWTConfig()
	mw := NewAdminAuthMiddleware(config)

	t.Run("missing token", func(t *testing.T) {
		ctx := &mockContext{headers: map[string]string{}}
		err := mw.Authenticate(ctx)
		assert.ErrorIs(t, err, ErrMissingToken)
	})

	t.Run("invalid token", func(t *testing.T) {
		ctx := &mockContext{headers: map[string]string{"x-api-token": "invalid"}}
		err := mw.Authenticate(ctx)
		assert.Error(t, err) // Could be ErrInvalidSigningMethod or other parsing error
	})

	t.Run("not admin", func(t *testing.T) {
		token, err := generateTestJWT(false, "testsecret")
		if err != nil {
			t.Fatal(err)
		}
		ctx := &mockContext{headers: map[string]string{"x-api-token": token}}
		err = mw.Authenticate(ctx)
		assert.ErrorIs(t, err, ErrNotAdmin)
	})

	t.Run("is admin", func(t *testing.T) {
		token, err := generateTestJWT(true, "testsecret")
		if err != nil {
			t.Fatal(err)
		}
		ctx := &mockContext{headers: map[string]string{"x-api-token": token}}
		err = mw.Authenticate(ctx)
		assert.NoError(t, err)
	})
}
