package api

import (
	"context"
	"encoding/json"
	"net/http"
	"os"
	"time"

	"github.com/anthdm/ggcommerce/store"
	"github.com/anthdm/ggcommerce/types"
	"github.com/golang-jwt/jwt/v4"
)

// Define the ContextAPI interface for handler context
// This interface should match the methods used in handlers
// (Request, ResponseWriter, Param, Context, JSON, Header)
type ContextAPI interface {
	Request() *http.Request
	ResponseWriter() http.ResponseWriter
	Param(string) string
	Context() context.Context
	JSON(int, interface{}) error
	Header(string) string
}

type UserHandler struct {
	store store.UserStorer
}

func NewUserHandler(uStore store.UserStorer) *UserHandler {
	return &UserHandler{
		store: uStore,
	}
}

// HandlePostUser creates a new user from the request body
func (h *UserHandler) HandlePostUser(c ContextAPI) error {
	userReq := &types.CreateUserRequest{}
	if err := json.NewDecoder(c.Request().Body).Decode(userReq); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	// Validate input via NewUserFromRequest which calls validateCreateUserRequest
	user, err := types.NewUserFromRequest(userReq)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": err.Error()})
	}

	if err := h.store.Insert(c.Context(), user); err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to insert user"})
	}

	return c.JSON(http.StatusCreated, user)
}

// HandleGetUsers returns all users
func (h *UserHandler) HandleGetUsers(c ContextAPI) error {
	users, err := h.store.GetAll(c.Context())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch users"})
	}
	return c.JSON(http.StatusOK, users)
}

// HandleGetUserByID returns a user by ID
func (h *UserHandler) HandleGetUserByID(c ContextAPI) error {
	id := c.Param("id")
	user, err := h.store.GetByID(c.Context(), id)
	if err != nil {
		// Check for mongo.ErrNoDocuments (not found)
		if err.Error() == "mongo: no documents in result" {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "User not found"})
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to fetch user"})
	}
	return c.JSON(http.StatusOK, user)
}

// LoginResponse represents the response for a successful login
type LoginResponse struct {
	Token string      `json:"token"`
	User  *types.User `json:"user"`
}

// HandleLogin authenticates a user and returns a JWT token
func (h *UserHandler) HandleLogin(c ContextAPI) error {
	loginReq := &struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}{}

	if err := json.NewDecoder(c.Request().Body).Decode(loginReq); err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request body"})
	}

	user, err := h.store.GetByEmail(c.Context(), loginReq.Email)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid credentials"})
	}

	if !user.ValidatePassword(loginReq.Password) {
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid credentials"})
	}

	// Generate JWT token
	token, err := generateJWT(user)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate token"})
	}

	// Update user's token in the database
	user.Token = token
	// Note: In a real application, you might want to update the user in the database with the new token

	return c.JSON(http.StatusOK, &LoginResponse{
		Token: token,
		User:  user,
	})
}

func generateJWT(user *types.User) (string, error) {
	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "your-secret-key" // Default secret for development
	}

	// Create the Claims
	claims := jwt.MapClaims{
		"userID":  user.ID,
		"email":   user.Email,
		"isAdmin": user.IsAdmin,
		"exp":     time.Now().Add(time.Hour * 72).Unix(), // Token expires in 72 hours
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Generate encoded token
	tokenString, err := token.SignedString([]byte(secret))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}
