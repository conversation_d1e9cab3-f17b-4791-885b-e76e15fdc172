package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"sync"
	"testing"

	"github.com/anthdm/ggcommerce/types"
	"github.com/anthdm/weavebox"
	"github.com/stretchr/testify/assert"
)

type inMemoryUserStore struct {
	mu    sync.Mutex
	users map[string]*types.User // keyed by email
}

func newInMemoryUserStore() *inMemoryUserStore {
	return &inMemoryUserStore{
		users: make(map[string]*types.User),
	}
}

func (s *inMemoryUserStore) Insert(ctx context.Context, user *types.User) error {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.users[user.Email] = user
	return nil
}
func (s *inMemoryUserStore) GetAll(ctx context.Context) ([]*types.User, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	var out []*types.User
	for _, u := range s.users {
		out = append(out, u)
	}
	return out, nil
}
func (s *inMemoryUserStore) GetByID(ctx context.Context, id string) (*types.User, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	for _, u := range s.users {
		if u.ID == id {
			return u, nil
		}
	}
	return nil, nil // or errors.New("not found")
}
func (s *inMemoryUserStore) GetByEmail(ctx context.Context, email string) (*types.User, error) {
	s.mu.Lock()
	defer s.mu.Unlock()
	u, ok := s.users[email]
	if !ok {
		return nil, nil // or errors.New("not found")
	}
	return u, nil
}

// Adapter to allow ContextAPI handlers to be used with weavebox
// Wraps *weavebox.Context to implement ContextAPI

type weaveboxContextAdapter struct {
	ctx *weavebox.Context
}

func (w *weaveboxContextAdapter) Request() *http.Request              { return w.ctx.Request() }
func (w *weaveboxContextAdapter) ResponseWriter() http.ResponseWriter { return nil } // Not used in handlers
func (w *weaveboxContextAdapter) Param(key string) string             { return w.ctx.Param(key) }
func (w *weaveboxContextAdapter) Context() context.Context            { return w.ctx.Request().Context() }
func (w *weaveboxContextAdapter) JSON(code int, v interface{}) error  { return w.ctx.JSON(code, v) }
func (w *weaveboxContextAdapter) Header(key string) string            { return w.ctx.Request().Header.Get(key) }

func adaptToWeavebox(handler func(ContextAPI) error) func(*weavebox.Context) error {
	return func(ctx *weavebox.Context) error {
		return handler(&weaveboxContextAdapter{ctx: ctx})
	}
}

func setupTestServer() http.Handler {
	userStore := newInMemoryUserStore()
	userHandler := NewUserHandler(userStore)

	app := weavebox.New()
	app.Post("/users", adaptToWeavebox(userHandler.HandlePostUser))
	app.Post("/login", adaptToWeavebox(userHandler.HandleLogin))
	// Add more routes as needed

	return app
}

func TestE2E_UserFlow(t *testing.T) {
	os.Setenv("JWT_SECRET", "testsecret")
	defer os.Unsetenv("JWT_SECRET")

	server := httptest.NewServer(setupTestServer())
	defer server.Close()

	// 1. Register a user
	userReq := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "hunter2001",
		"isAdmin":  true,
	}
	body, _ := json.Marshal(userReq)
	resp, err := http.Post(server.URL+"/users", "application/json", bytes.NewReader(body))
	assert.NoError(t, err)
	assert.Equal(t, http.StatusCreated, resp.StatusCode)

	// 2. Login as the user
	loginReq := map[string]interface{}{
		"email":    "<EMAIL>",
		"password": "hunter2001",
	}
	body, _ = json.Marshal(loginReq)
	resp, err = http.Post(server.URL+"/login", "application/json", bytes.NewReader(body))
	assert.NoError(t, err)
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var loginResp struct {
		Token string `json:"token"`
	}
	_ = json.NewDecoder(resp.Body).Decode(&loginResp)
	assert.NotEmpty(t, loginResp.Token)
}
