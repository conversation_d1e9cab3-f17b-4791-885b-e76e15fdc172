package store

import (
	"context"
	"errors"

	"github.com/anthdm/ggcommerce/types"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoProductStore struct {
	db   *mongo.Database
	coll string
}

func NewMongoProductStore(db *mongo.Database) *MongoProductStore {
	return &MongoProductStore{
		db:   db,
		coll: "products",
	}
}

func (s *MongoProductStore) Insert(ctx context.Context, p *types.Product) error {
	res, err := s.db.Collection(s.coll).InsertOne(ctx, p)
	if err != nil {
		return err
	}
	objID, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return errors.New("failed to convert InsertedID to ObjectID")
	}
	p.ID = objID.Hex()
	return nil
}

func (s *MongoProductStore) GetAll(ctx context.Context) ([]*types.Product, error) {
	cursor, err := s.db.Collection(s.coll).Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	var products []*types.Product
	if err := cursor.All(ctx, &products); err != nil {
		return nil, err
	}
	return products, nil
}

func (s *MongoProductStore) GetByID(ctx context.Context, id string) (*types.Product, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("invalid product ID format")
	}
	res := s.db.Collection(s.coll).FindOne(ctx, bson.M{"_id": objID})
	p := &types.Product{}
	if err := res.Decode(p); err != nil {
		return nil, err
	}
	return p, nil
}
