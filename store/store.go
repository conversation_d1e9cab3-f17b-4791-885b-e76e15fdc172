package store

import (
	"context"

	"github.com/anthdm/ggcommerce/types"
)

type ProductStorer interface {
	Insert(context.Context, *types.Product) error
	GetByID(context.Context, string) (*types.Product, error)
	GetAll(context.Context) ([]*types.Product, error)
}

type UserStorer interface {
	Insert(context.Context, *types.User) error
	GetByID(context.Context, string) (*types.User, error)
	GetAll(context.Context) ([]*types.User, error)
	GetByEmail(context.Context, string) (*types.User, error)
}
