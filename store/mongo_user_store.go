package store

import (
	"context"
	"errors"

	"github.com/anthdm/ggcommerce/types"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoUserStore struct {
	db   *mongo.Database
	coll string
}

func NewMongoUserStore(db *mongo.Database) *MongoUserStore {
	return &MongoUserStore{
		db:   db,
		coll: "users",
	}
}

func (s *MongoUserStore) Insert(ctx context.Context, u *types.User) error {
	res, err := s.db.Collection(s.coll).InsertOne(ctx, u)
	if err != nil {
		return err
	}
	objID, ok := res.InsertedID.(primitive.ObjectID)
	if !ok {
		return errors.New("failed to convert InsertedID to ObjectID")
	}
	u.ID = objID.Hex()
	return nil
}

func (s *MongoUserStore) GetAll(ctx context.Context) ([]*types.User, error) {
	cursor, err := s.db.Collection(s.coll).Find(ctx, bson.M{})
	if err != nil {
		return nil, err
	}

	var users []*types.User
	if err := cursor.All(ctx, &users); err != nil {
		return nil, err
	}
	return users, nil
}

func (s *MongoUserStore) GetByID(ctx context.Context, id string) (*types.User, error) {
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, errors.New("invalid user ID format")
	}
	res := s.db.Collection(s.coll).FindOne(ctx, bson.M{"_id": objID})
	u := &types.User{}
	if err := res.Decode(u); err != nil {
		return nil, err
	}
	return u, nil
}

func (s *MongoUserStore) GetByEmail(ctx context.Context, email string) (*types.User, error) {
	res := s.db.Collection(s.coll).FindOne(ctx, bson.M{"email": email})
	u := &types.User{}
	if err := res.Decode(u); err != nil {
		return nil, err
	}
	return u, nil
}
