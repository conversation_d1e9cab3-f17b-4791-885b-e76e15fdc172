package main

import (
	"context"
	"fmt"
	"net/http"

	"github.com/anthdm/ggcommerce/api"
	"github.com/anthdm/ggcommerce/store"

	"github.com/anthdm/weavebox"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// weaveboxHeaderAdapter adapts weavebox.Context to implement api.HeaderProvider
type weaveboxHeaderAdapter struct {
	ctx *weavebox.Context
}

func (w *weaveboxHeaderAdapter) Header(key string) string {
	return w.ctx.Request().Header.Get(key)
}

// weaveboxContextAdapter adapts weavebox.Context to implement api.ContextAPI
type weaveboxContextAdapter struct {
	ctx *weavebox.Context
}

func (w *weaveboxContextAdapter) Request() *http.Request              { return w.ctx.Request() }
func (w *weaveboxContextAdapter) ResponseWriter() http.ResponseWriter { return nil } // Not used in handlers
func (w *weaveboxContextAdapter) Param(key string) string             { return w.ctx.Param(key) }
func (w *weaveboxContextAdapter) Context() context.Context            { return w.ctx.Request().Context() }
func (w *weaveboxContextAdapter) JSON(code int, v interface{}) error  { return w.ctx.JSON(code, v) }
func (w *weaveboxContextAdapter) Header(key string) string            { return w.ctx.Request().Header.Get(key) }

// adaptUserHandler creates a weavebox handler from a ContextAPI handler
func adaptUserHandler(handler func(api.ContextAPI) error) func(*weavebox.Context) error {
	return func(ctx *weavebox.Context) error {
		return handler(&weaveboxContextAdapter{ctx: ctx})
	}
}

func handleAPIError(ctx *weavebox.Context, err error) {
	fmt.Println("API error:", err)
	ctx.JSON(http.StatusBadRequest, map[string]string{"error": err.Error()})
}

func main() {
	app := weavebox.New()
	app.ErrorHandler = handleAPIError

	jwtConfig := api.NewJWTConfig()
	adminMW := api.NewAdminAuthMiddleware(jwtConfig)
	adminRoute := app.Box("/admin")
	
	// Create a weavebox middleware adapter
	adminRoute.Use(func(ctx *weavebox.Context) error {
		// Adapter to make weavebox.Context implement HeaderProvider
		headerProvider := &weaveboxHeaderAdapter{ctx: ctx}
		return adminMW.Authenticate(headerProvider)
	})

	client, err := mongo.Connect(context.TODO(), options.Client().ApplyURI("mongodb://localhost:27017"))
	if err != nil {
		panic(err)
	}

	// Initialize stores
	productStore := store.NewMongoProductStore(client.Database("ggcommerce"))
	userStore := store.NewMongoUserStore(client.Database("ggcommerce"))

	// Initialize handlers
	productHandler := api.NewProductHandler(productStore)
	userHandler := api.NewUserHandler(userStore)

	// Public routes
	app.Post("/login", adaptUserHandler(userHandler.HandleLogin))
	app.Post("/users", adaptUserHandler(userHandler.HandlePostUser))

	// Admin product routes
	adminProductRoute := adminRoute.Box("/product")
	adminProductRoute.Get("/:id", productHandler.HandleGetProductByID)
	adminProductRoute.Get("/", productHandler.HandleGetProducts)
	adminProductRoute.Post("/", productHandler.HandlePostProduct)

	// Admin user routes
	adminUserRoute := adminRoute.Box("/user")
	adminUserRoute.Get("/:id", adaptUserHandler(userHandler.HandleGetUserByID))
	adminUserRoute.Get("/", adaptUserHandler(userHandler.HandleGetUsers))

	app.Serve(3001)
}
