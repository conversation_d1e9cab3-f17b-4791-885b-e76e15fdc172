package types

import (
	"fmt"

	"golang.org/x/crypto/bcrypt"
)

type User struct {
	ID                string `bson:"_id,omitempty" json:"id"`
	Email             string `bson:"email" json:"email"`
	EncryptedPassword string `bson:"encryptedPassword" json:"_"`
	IsAdmin           bool   `bson:"isAdmin" json:"isAdmin"`
	Token             string `bson:"token" json:"token"`
}

type CreateUserRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
	IsAdmin  bool   `json:"isAdmin"`
}

func NewUserFromRequest(req *CreateUserRequest) (*User, error) {
	if err := validateCreateUserRequest(req); err != nil {
		return nil, err
	}

	epw, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	return &User{
		Email:             req.Email,
		EncryptedPassword: string(epw),
		IsAdmin:           req.IsAdmin,
	}, nil
}

func validateCreateUserRequest(req *CreateUserRequest) error {
	if len(req.Email) < 5 {
		return fmt.Errorf("email is too short")
	}
	if len(req.Password) < 6 {
		return fmt.Errorf("password is too short, minimum 6 characters required")
	}
	return nil
}

func NewAdminUser(email, password string) (*User, error) {
	user, err := NewUser(email, password)
	if err != nil {
		return nil, err
	}
	user.IsAdmin = true
	return user, nil
}

func NewUser(email, password string) (*User, error) {
	epw, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	return &User{
		Email:             email,
		EncryptedPassword: string(epw),
	}, nil
}

func (u *User) ValidatePassword(pw string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.EncryptedPassword), []byte(pw))
	return err == nil
}
