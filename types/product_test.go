package types

import (
	"testing"
	"github.com/stretchr/testify/assert"
)

func TestNewProductFromRequest_Valid(t *testing.T) {
	req := &CreateProductRequest{
		SKU:  "ABC123",
		Name: "Test Product",
	}
	product, err := NewProductFromRequest(req)
	assert.Nil(t, err)
	assert.NotNil(t, product)
	assert.Equal(t, req.SKU, product.SKU)
	assert.Equal(t, req.Name, product.Name)
	assert.Equal(t, "test-product", product.Slug)
}

func TestNewProductFromRequest_InvalidSKU(t *testing.T) {
	req := &CreateProductRequest{
		SKU:  "A",
		Name: "Valid Name",
	}
	product, err := NewProductFromRequest(req)
	assert.NotNil(t, err)
	assert.Nil(t, product)
}

func TestNewProductFromRequest_InvalidName(t *testing.T) {
	req := &CreateProductRequest{
		SKU:  "ABC123",
		Name: "No",
	}
	product, err := NewProductFromRequest(req)
	assert.NotNil(t, err)
	assert.Nil(t, product)
}
