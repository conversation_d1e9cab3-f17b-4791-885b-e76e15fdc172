package types

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewUserFromRequest_Valid(t *testing.T) {
	req := &CreateUserRequest{
		Email:    "<EMAIL>",
		Password: "password123",
		IsAdmin:  false,
	}
	user, err := NewUserFromRequest(req)
	assert.<PERSON><PERSON>(t, err)
	assert.NotNil(t, user)
	assert.Equal(t, req.Email, user.Email)
	assert.NotEmpty(t, user.EncryptedPassword)
	assert.Equal(t, req.IsA<PERSON><PERSON>, user.IsAdmin)
}

func TestNewUserFromRequest_InvalidEmail(t *testing.T) {
	req := &CreateUserRequest{
		Email:    "a@b", // Too short
		Password: "password123",
		IsAdmin:  false,
	}
	user, err := NewUserFromRequest(req)
	assert.NotNil(t, err)
	assert.Nil(t, user)
}

func TestNewUserFromRequest_InvalidPassword(t *testing.T) {
	req := &CreateUserRequest{
		Email:    "<EMAIL>",
		Password: "12345", // Too short
		IsAdmin:  false,
	}
	user, err := NewUserFromRequest(req)
	assert.NotNil(t, err)
	assert.Nil(t, user)
}
